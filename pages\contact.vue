<template>
  <main class="bg-transparent">
    <!-- SEO Component -->
    <SEO
      title="Contact Me"
      description="Get in touch with me through this contact form or connect with me on social media platforms."
      url="/contact"
    />

    <div class="container mx-auto px-4 py-6 md:py-8">
      <div class="max-w-6xl mx-auto">
        <!-- Page Title with Animation -->
        <h1
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { duration: 700 } }"
          class="text-3xl md:text-4xl font-bold mb-6 md:mb-8 text-center"
          :style="{
            background: getGradientStyle().backgroundImage,
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textFillColor: 'transparent'
          }"
        >
          Get In Touch
        </h1>

        <!-- Main Content - Side by Side Layout -->
        <div class="flex flex-col lg:flex-row gap-6">
          <!-- Contact Form Container - Left Side -->
          <div
            v-motion
            :initial="{ opacity: 0 }"
            :enter="{ opacity: 1, transition: { duration: 600, delay: 300 } }"
            class="w-full lg:w-3/5"
          >
            <GlassContainer>
              <ContactForm />
            </GlassContainer>
          </div>

          <!-- Contact Info - Right Side -->
          <div
            v-motion
            :initial="{ opacity: 0 }"
            :enter="{ opacity: 1, transition: { duration: 600, delay: 500 } }"
            class="w-full lg:w-2/5"
          >
            <GlassContainer class="h-full">
              <!-- Social Links -->
              <div class="mb-8">
                <h2 class="text-xl font-semibold mb-4 project-theme-text" :style="{ color: themeStore.themeColors.primary }">
                  Connect With Me
                </h2>
                <div class="flex flex-col space-y-3">
                  <!-- GitHub -->
                  <a
                    v-motion
                    :initial="{ opacity: 0 }"
                    :enter="{ opacity: 1, transition: { duration: 500, delay: 700 } }"
                    href="https://github.com/FadiNahhas"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="social-link flex items-center px-4 py-3 rounded-lg transition-all duration-300"
                    :style="{
                      backgroundColor: themeStore.isGameDev ? 'rgba(0, 255, 65, 0.2)' : 'rgba(0, 163, 255, 0.2)',
                      border: `1px solid ${themeStore.isGameDev ? '#00FF41' : '#00A3FF'}`,
                      boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)'
                    }"
                    @mouseenter="animateSocialLink($event)"
                    @mouseleave="resetSocialLink($event)"
                  >
                    <Icon icon="mdi:github" class="w-6 h-6 mr-3 text-white" />
                    <span class="text-white font-medium">GitHub</span>
                  </a>

                  <!-- LinkedIn -->
                  <a
                    v-motion
                    :initial="{ opacity: 0 }"
                    :enter="{ opacity: 1, transition: { duration: 500, delay: 800 } }"
                    href="https://www.linkedin.com/in/fadi-nahhas/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="social-link flex items-center px-4 py-3 rounded-lg transition-all duration-300"
                    :style="{
                      backgroundColor: themeStore.isGameDev ? 'rgba(0, 255, 65, 0.2)' : 'rgba(0, 163, 255, 0.2)',
                      border: `1px solid ${themeStore.isGameDev ? '#00FF41' : '#00A3FF'}`,
                      boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)'
                    }"
                    @mouseenter="animateSocialLink($event)"
                    @mouseleave="resetSocialLink($event)"
                  >
                    <Icon icon="mdi:linkedin" class="w-6 h-6 mr-3 text-white" />
                    <span class="text-white font-medium">LinkedIn</span>
                  </a>

                  <!-- Instagram -->
                  <a
                    v-motion
                    :initial="{ opacity: 0 }"
                    :enter="{ opacity: 1, transition: { duration: 500, delay: 900 } }"
                    href="https://www.instagram.com/fadinahhas/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="social-link flex items-center px-4 py-3 rounded-lg transition-all duration-300"
                    :style="{
                      backgroundColor: themeStore.isGameDev ? 'rgba(0, 255, 65, 0.2)' : 'rgba(0, 163, 255, 0.2)',
                      border: `1px solid ${themeStore.isGameDev ? '#00FF41' : '#00A3FF'}`,
                      boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)'
                    }"
                    @mouseenter="animateSocialLink($event)"
                    @mouseleave="resetSocialLink($event)"
                  >
                    <Icon icon="mdi:instagram" class="w-6 h-6 mr-3 text-white" />
                    <span class="text-white font-medium">Instagram</span>
                  </a>
                </div>
              </div>

              <!-- Contact Details -->
              <div>
                <h2 class="text-xl font-semibold mb-4 project-theme-text" :style="{ color: themeStore.themeColors.primary }">
                  Contact Details
                </h2>
                <ul class="space-y-3">
                  <li class="flex items-start">
                    <Icon icon="mdi:email" class="w-5 h-5 mt-0.5 mr-3 project-theme-text" :style="{ color: themeStore.themeColors.primary }" />
                    <span class="text-white"><EMAIL></span>
                  </li>
                  <li class="flex items-start">
                    <Icon icon="mdi:map-marker" class="w-5 h-5 mt-0.5 mr-3 project-theme-text" :style="{ color: themeStore.themeColors.primary }" />
                    <span class="text-white">Ma'alot Tarshiha, Israel</span>
                  </li>
                </ul>
              </div>
            </GlassContainer>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { gsap } from 'gsap';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '~/stores/theme-optimized';
import { useHead } from '#app';

// SEO is now handled by the SEO component

// Get the theme store
const themeStore = useThemeStore();

// Refs for animations
const pageTitle = ref(null);

// We're now using hardcoded social links in the template

// Function to get gradient style based on theme
const getGradientStyle = () => {
  if (themeStore.isProjectPage && themeStore.themeColors) {
    // Use project-specific colors
    const primary = themeStore.themeColors.primary || '#00FF41';
    const secondary = themeStore.themeColors.secondary || '#008F11';
    return {
      backgroundImage: `linear-gradient(to right, ${primary}, ${secondary})`
    };
  } else if (themeStore.isGameDev) {
    // Game dev theme (green)
    return {
      backgroundImage: 'linear-gradient(to right, #00FF41, #008F11)'
    };
  } else {
    // Web dev theme (blue)
    return {
      backgroundImage: 'linear-gradient(to right, #00A3FF, #0077B6)'
    };
  }
};

// Function to get social link style based on theme
const getSocialLinkStyle = () => {
  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');
  const secondary = themeStore.themeColors.secondary || (themeStore.isGameDev ? '#008F11' : '#0077B6');

  return {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    border: `1px solid ${primary}`,
    boxShadow: `0 0 10px rgba(0, 0, 0, 0.3)`,
    color: 'white' // Ensure text is visible
  };
};

// Track if initial animation is complete
const initialAnimationComplete = ref(false);

// Set initial animation as complete after a delay
onMounted(() => {
  setTimeout(() => {
    initialAnimationComplete.value = true;
  }, 2000); // 2 seconds should be enough for initial animations to complete
});

// Function to animate social link on hover
const animateSocialLink = (event) => {
  // Skip hover animation if initial animation isn't complete
  if (!initialAnimationComplete.value) return;

  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');
  const rgbValues = hexToRgb(primary);

  // Kill any existing animations on this element
  gsap.killTweensOf(event.currentTarget);

  // Also kill animations on child elements
  const icon = event.currentTarget.querySelector('svg');
  const text = event.currentTarget.querySelector('span');
  if (icon) gsap.killTweensOf(icon);
  if (text) gsap.killTweensOf(text);

  // Now apply the hover animation
  gsap.to(event.currentTarget, {
    backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.4)`,
    transform: 'translateX(5px)',
    boxShadow: `0 0 15px rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.3)`,
    duration: 0.3
  });

  // Animate the icon
  if (icon) {
    gsap.to(icon, {
      x: 3,
      scale: 1.1,
      duration: 0.3
    });
  }

  // Make text brighter
  if (text) {
    gsap.to(text, {
      fontWeight: 700,
      duration: 0.3
    });
  }
};

// Function to reset social link animation
const resetSocialLink = (event) => {
  // Skip reset animation if initial animation isn't complete
  if (!initialAnimationComplete.value) return;

  // Kill any existing animations on this element
  gsap.killTweensOf(event.currentTarget);

  // Also kill animations on child elements
  const icon = event.currentTarget.querySelector('svg');
  const text = event.currentTarget.querySelector('span');
  if (icon) gsap.killTweensOf(icon);
  if (text) gsap.killTweensOf(text);

  // Now apply the reset animation
  gsap.to(event.currentTarget, {
    backgroundColor: themeStore.isGameDev ? 'rgba(0, 255, 65, 0.2)' : 'rgba(0, 163, 255, 0.2)',
    transform: 'translateX(0)',
    boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
    duration: 0.3
  });

  // Reset icon animation
  if (icon) {
    gsap.to(icon, {
      x: 0,
      scale: 1,
      duration: 0.3
    });
  }

  // Reset text
  if (text) {
    gsap.to(text, {
      fontWeight: 500,
      duration: 0.3
    });
  }
};

// Helper function to convert hex to RGB
const hexToRgb = (hex) => {
  // Remove # if present
  hex = hex.replace('#', '');

  // Handle shorthand hex
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Parse hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
};

// Function to get a darkened version of the primary color for better readability
const getDarkenedPrimary = () => {
  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');
  const rgb = hexToRgb(primary);

  // Darken the color by reducing each RGB component by 30%
  const darkenedR = Math.max(0, Math.floor(rgb.r * 0.7));
  const darkenedG = Math.max(0, Math.floor(rgb.g * 0.7));
  const darkenedB = Math.max(0, Math.floor(rgb.b * 0.7));

  return `rgb(${darkenedR}, ${darkenedG}, ${darkenedB})`;
};

// Set initial animation as complete after a delay
onMounted(() => {
  setTimeout(() => {
    initialAnimationComplete.value = true;
  }, 2000); // 2 seconds should be enough for initial animations to complete
});
</script>

<style scoped>
.social-link {
  transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid var(--primary-color);
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.social-link:hover::before {
  transform: translateX(100%);
}

/* Add a subtle glow to social links to make them more visible */
.social-link {
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.social-link svg {
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
}

.social-link span {
  color: white !important;
  font-weight: 500;
}
</style>
