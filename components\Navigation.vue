<template>
  <nav
    ref="navbar"
    class="fixed w-full z-50 backdrop-blur-md bg-black/60 transition-all duration-300 navbar-glass"
    style="position: fixed; top: 0; left: 0; right: 0;"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16 md:h-20">
        <!-- Logo with hover animation -->
        <NuxtLink
          to="/"
          class="text-xl md:text-2xl font-bold relative overflow-hidden group"
          @mouseenter="animateLogoHighlight(true)"
          @mouseleave="animateLogoHighlight(false)"
        >
          <span
            class="relative z-10 bg-clip-text text-transparent project-theme-gradient"
            :style="getLogoGradientStyle"
          >
            Fadi <PERSON>
          </span>
          <span
            ref="logoHighlight"
            class="absolute bottom-0 left-0 w-0 h-0.5"
            :style="{
              background: getLogoGradientStyle.background || `linear-gradient(to right, ${themeStore.themeColors.primary}, ${themeStore.themeColors.secondary})`
            }"
          ></span>
        </NuxtLink>

        <!-- Mobile menu button with morphing animation -->
        <button
          ref="mobileMenuButton"
          class="md:hidden flex items-center p-2 rounded-md text-gray-300 hover:text-white focus:outline-none relative overflow-hidden"
          @click="toggleMobileMenu"
        >
          <!-- Animated hamburger lines -->
          <div class="w-6 h-6 flex flex-col justify-center items-center relative">
            <span ref="hamburgerLine1" class="block w-5 h-0.5 bg-current transition-all duration-300 absolute"></span>
            <span ref="hamburgerLine2" class="block w-5 h-0.5 bg-current transition-all duration-300 absolute"></span>
            <span ref="hamburgerLine3" class="block w-5 h-0.5 bg-current transition-all duration-300 absolute"></span>
          </div>

          <!-- Ripple effect background -->
          <div ref="buttonRipple" class="absolute inset-0 rounded-md opacity-0 pointer-events-none"
               :style="{ backgroundColor: `${themeStore.themeColors.primary}20` }"></div>
        </button>

        <!-- Desktop Navigation Links -->
        <div class="hidden md:flex space-x-8">
          <div v-for="(link, index) in navLinks" :key="link.path" class="relative">
            <!-- Navigation Link -->
            <div class="relative flex flex-col items-center">
              <NuxtLink
                :to="link.path"
                class="py-2 px-1 font-medium text-gray-300 transition-colors duration-300 hover:text-white nav-link group"
                :class="{ 'text-white': isActiveRoute(link.path) }"
                @mouseenter="createRipple($event, index)"
                ref="navLinkRefs"
              >
                <div class="flex items-center space-x-1">
                  <Icon
                    :icon="link.icon"
                    class="w-5 h-5 transition-colors duration-300"
                    :style="isActiveRoute(link.path) ? { color: getActiveIconColor } : {}"
                  />
                  <span>{{ link.name }}</span>
                </div>
              </NuxtLink>

              <!-- Indicator - visible when active, hidden when inactive -->
              <div
                :ref="`indicator-${index}`"
                class="rounded-full mt-1 transform transition-all duration-300 indicator-bar project-theme-gradient"
                :class="{
                  'scale-x-100 opacity-100': isActiveRoute(link.path),
                  'scale-x-0 opacity-0': !isActiveRoute(link.path)
                }"
                :style="{
                  width: '80%',
                  height: '3px',
                  'transform-origin': 'center',
                  ...(isActiveRoute(link.path) ? getLogoGradientStyle : {})
                }"
              ></div>
            </div>

            <!-- Particles container for each link - hidden by default -->
            <div :ref="`particles-${index}`" class="absolute pointer-events-none opacity-0"></div>
          </div>
        </div>
      </div>

      <!-- Mobile Navigation Menu with backdrop -->
      <div
        v-show="mobileMenuOpen"
        class="md:hidden fixed inset-x-0 top-16 z-40 opacity-0"
        ref="mobileMenuContainer"
        style="visibility: hidden;"
      >
        <!-- Backdrop blur -->
        <div
          ref="mobileBackdrop"
          class="absolute inset-0 backdrop-blur-md bg-black/60 opacity-0"
          @click="closeMobileMenu"
        ></div>

        <!-- Menu content -->
        <div
          class="relative bg-black/80 backdrop-blur-lg border-t border-gray-700/50 shadow-2xl"
          ref="mobileMenu"
          style="height: 0; overflow: hidden;"
        >
          <div class="py-2 space-y-1">
            <NuxtLink
              v-for="(link, index) in navLinks"
              :key="link.path"
              :to="link.path"
              class="block py-4 px-6 font-medium mobile-nav-link relative overflow-hidden group"
              :class="{
                'bg-black/40 text-white border-l-4': isActiveRoute(link.path),
                'text-gray-300 hover:bg-black/30 hover:text-white': !isActiveRoute(link.path)
              }"
              :style="isActiveRoute(link.path) ? { borderColor: getActiveIconColor } : {}"
              @click="closeMobileMenu"
              @mouseenter="createMobileNavHover($event, index)"
              @mouseleave="removeMobileNavHover($event, index)"
            >
              <!-- Hover background effect -->
              <div
                class="absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none mobile-nav-bg"
                :style="{ backgroundColor: `${themeStore.themeColors.primary}15` }"
              ></div>

              <div class="flex items-center space-x-4 relative z-10">
                <Icon
                  :icon="link.icon"
                  class="w-6 h-6 transition-all duration-300"
                  :style="isActiveRoute(link.path) ? { color: getActiveIconColor } : {}"
                />
                <span class="text-lg">{{ link.name }}</span>

                <!-- Arrow indicator for active/hover -->
                <Icon
                  icon="mdi:chevron-right"
                  class="w-5 h-5 ml-auto transition-all duration-300 mobile-nav-arrow"
                  :style="{ color: themeStore.themeColors.primary }"
                />
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import { gsap } from 'gsap';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '~/stores/theme-optimized';

// Refs for DOM elements
const navbar = ref(null);
const logoHighlight = ref(null);
const navLinkRefs = ref([]);
const mobileMenu = ref(null);
const mobileMenuContainer = ref(null);
const mobileBackdrop = ref(null);
const mobileMenuButton = ref(null);
const hamburgerLine1 = ref(null);
const hamburgerLine2 = ref(null);
const hamburgerLine3 = ref(null);
const buttonRipple = ref(null);

// Get the theme store
const themeStore = useThemeStore();

// Navigation links data with icons
const navLinks = [
  { name: 'Home', path: '/', icon: 'mdi:home' },
  { name: 'About', path: '/about', icon: 'mdi:account' },
  { name: 'Projects', path: '/projects', icon: 'mdi:briefcase' },
  { name: 'Contact', path: '/contact', icon: 'mdi:email' }
];

// Ensure this style is applied consistently on both server and client
const getLogoGradientStyle = computed(() => {
  // Use computed values to ensure consistency between SSR and client
  // For project pages, use the project theme colors
  if (themeStore.isProjectPage && themeStore.projectTheme) {
    const primary = themeStore.projectTheme.primary || themeStore.projectTheme.colors?.primary || themeStore.themeColors.primary;
    const secondary = themeStore.projectTheme.secondary || themeStore.projectTheme.colors?.secondary || primary;

    return {
      background: `linear-gradient(to right, ${primary}, ${secondary})`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent'
    };
  }

  // For non-project pages, use the theme based on isGameDev
  const colors = themeStore.themeColors;
  return {
    background: `linear-gradient(to right, ${colors.primary}, ${colors.secondary})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent'
  };
});

// Get current route
const route = useRoute();

// State variables
const isScrolled = ref(false);
const mobileMenuOpen = ref(false);

// Check if a route is active
const isActiveRoute = (path) => {
  if (path === '/') {
    return route.path === '/';
  }
  return route.path.startsWith(path);
};

// Get the active icon color based on current theme
const getActiveIconColor = computed(() => {
  if (themeStore.isProjectPage && themeStore.projectTheme) {
    return themeStore.projectTheme.primary || themeStore.projectTheme.colors?.primary || themeStore.themeColors.primary;
  }
  return themeStore.themeColors.primary;
});

// Fancy GSAP animation for indicator on hover
const createRipple = (_event, index) => {
  // Only handle the indicator
  const indicator = document.querySelector(`[ref="indicator-${index}"]`);

  if (indicator && !isActiveRoute(navLinks[index].path)) {
    // Create a fancy hover animation with GSAP
    const hoverTl = gsap.timeline();

    // Animate from left to right with a slight bounce
    hoverTl.fromTo(indicator,
      {
        scaleX: 0,
        opacity: 0,
        transformOrigin: 'left center'
      },
      {
        scaleX: 1,
        opacity: 1,
        duration: 0.35,
        ease: 'power3.out'
      }
    )
    .to(indicator, {
      scaleX: 0.97,
      duration: 0.1,
      ease: 'power1.in'
    })
    .to(indicator, {
      scaleX: 1,
      duration: 0.15,
      ease: 'power1.out'
    });
  }
};

// Handle scroll events to change navbar appearance
const handleScroll = () => {
  isScrolled.value = window.scrollY > 20;
};

// Enhanced hamburger animation
const animateHamburger = (isOpen) => {
  const tl = gsap.timeline();

  if (isOpen) {
    // Transform to X - need to move lines to center first, then rotate
    tl.to(hamburgerLine1.value, {
      y: 0,
      rotation: 45,
      duration: 0.3,
      ease: 'power2.out'
    })
    .to(hamburgerLine2.value, {
      opacity: 0,
      scale: 0,
      duration: 0.2,
      ease: 'power2.out'
    }, 0)
    .to(hamburgerLine3.value, {
      y: 0,
      rotation: -45,
      duration: 0.3,
      ease: 'power2.out'
    }, 0);
  } else {
    // Transform back to hamburger
    tl.to([hamburgerLine1.value, hamburgerLine3.value], {
      rotation: 0,
      duration: 0.3,
      ease: 'elastic.out(1, 0.5)'
    })
    .to(hamburgerLine2.value, {
      opacity: 1,
      scale: 1,
      duration: 0.2,
      ease: 'back.out(1.7)'
    }, 0.1)
    // Reset line positions
    .to(hamburgerLine1.value, {
      y: -4,
      duration: 0.3,
      ease: 'elastic.out(1, 0.5)'
    }, 0)
    .to(hamburgerLine3.value, {
      y: 4,
      duration: 0.3,
      ease: 'elastic.out(1, 0.5)'
    }, 0);
  }

  return tl;
};

// Enhanced button ripple effect
const animateButtonRipple = () => {
  gsap.fromTo(buttonRipple.value, {
    transform: 'scale(0)',
    opacity: 0.6
  }, {
    transform: 'scale(1.5)',
    opacity: 0,
    duration: 0.6,
    ease: 'power2.out'
  });
};

// Enhanced mobile menu toggle
const toggleMobileMenu = () => {
  // Animate button ripple effect
  animateButtonRipple();

  if (mobileMenuOpen.value) {
    // Close menu with enhanced animation
    const tl = gsap.timeline({
      onComplete: () => {
        mobileMenuOpen.value = false;
        // Hide the container completely after animation
        if (mobileMenuContainer.value) {
          mobileMenuContainer.value.style.visibility = 'hidden';
        }
      }
    });

    // Animate hamburger back to normal
    animateHamburger(false);

    // Animate arrows out
    tl.to('.mobile-nav-arrow', {
      transform: 'translateX(-10px)',
      opacity: 0,
      duration: 0.2,
      ease: 'power2.in'
    })
    // Animate links out with enhanced stagger
    .to('.mobile-nav-link', {
      opacity: 0,
      transform: 'translateX(-30px) rotateY(-15deg)',
      stagger: {
        each: 0.05,
        from: 'end'
      },
      duration: 0.25,
      ease: 'power2.in'
    }, 0.1)
    // Animate backdrop out
    .to(mobileBackdrop.value, {
      opacity: 0,
      duration: 0.3,
      ease: 'power2.in'
    }, 0.2)
    // Collapse menu container
    .to(mobileMenu.value, {
      height: 0,
      duration: 0.4,
      ease: 'power3.inOut'
    }, 0.3)
    // Fade out the entire container
    .to(mobileMenuContainer.value, {
      opacity: 0,
      duration: 0.2,
      ease: 'power2.in'
    }, 0.5);
  } else {
    // Open menu with enhanced animation
    mobileMenuOpen.value = true;

    // Animate hamburger to X
    animateHamburger(true);

    // Wait for next tick to ensure the menu is in the DOM
    nextTick(() => {
      // First make the container visible
      if (mobileMenuContainer.value) {
        mobileMenuContainer.value.style.visibility = 'visible';
      }

      const tl = gsap.timeline();

      // Set initial states and make container visible
      gsap.set(mobileMenuContainer.value, { opacity: 1 });
      gsap.set(mobileBackdrop.value, { opacity: 0 });
      gsap.set(mobileMenu.value, { height: 0 });

      // Ensure links are in the correct starting position (they should already be set from onMounted)
      // But we'll set them again to be safe
      gsap.set('.mobile-nav-link', {
        opacity: 0,
        transform: 'translateX(50px) rotateY(15deg)'
      });
      gsap.set('.mobile-nav-arrow', {
        opacity: 0,
        transform: 'translateX(-10px)'
      });

      // Animate backdrop in
      tl.to(mobileBackdrop.value, {
        opacity: 1,
        duration: 0.4,
        ease: 'power2.out'
      })
      // Expand menu container
      .to(mobileMenu.value, {
        height: 'auto',
        duration: 0.5,
        ease: 'power3.out'
      }, 0.1)
      // Animate links in with enhanced stagger
      .to('.mobile-nav-link', {
        opacity: 1,
        transform: 'translateX(0px) rotateY(0deg)',
        stagger: {
          each: 0.08,
          from: 'start'
        },
        duration: 0.4,
        ease: 'back.out(1.4)'
      }, 0.3)
      // Animate arrows in
      .to('.mobile-nav-arrow', {
        transform: 'translateX(0px)',
        opacity: 0.6,
        stagger: 0.05,
        duration: 0.3,
        ease: 'power2.out'
      }, 0.5);
    });
  }
};

// Enhanced close mobile menu
const closeMobileMenu = () => {
  if (mobileMenuOpen.value) {
    toggleMobileMenu(); // Use the enhanced toggle function
  }
};

// Mobile nav hover effects
const createMobileNavHover = (event, index) => {
  const link = event.currentTarget;
  const bg = link.querySelector('.mobile-nav-bg');
  const arrow = link.querySelector('.mobile-nav-arrow');

  if (bg && arrow) {
    gsap.to(bg, {
      opacity: 1,
      duration: 0.3,
      ease: 'power2.out'
    });

    gsap.to(arrow, {
      opacity: 1,
      transform: 'translateX(5px)',
      duration: 0.3,
      ease: 'back.out(1.7)'
    });
  }
};

const removeMobileNavHover = (event, index) => {
  const link = event.currentTarget;
  const bg = link.querySelector('.mobile-nav-bg');
  const arrow = link.querySelector('.mobile-nav-arrow');

  if (bg && arrow) {
    gsap.to(bg, {
      opacity: 0,
      duration: 0.3,
      ease: 'power2.in'
    });

    gsap.to(arrow, {
      opacity: 0.6,
      transform: 'translateX(0px)',
      duration: 0.3,
      ease: 'power2.out'
    });
  }
};

// Watch for route changes to animate transitions
watch(() => route.path, (newPath, oldPath) => {
  // Find the indices of the old and new active links
  const oldIndex = navLinks.findIndex(link =>
    oldPath === link.path || (link.path !== '/' && oldPath.startsWith(link.path))
  );

  const newIndex = navLinks.findIndex(link =>
    newPath === link.path || (link.path !== '/' && newPath.startsWith(link.path))
  );

  // If we have valid indices, animate the transition
  if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
    // Create a timeline for the transition
    const tl = gsap.timeline();

    // Get the indicators
    const oldIndicator = document.querySelector(`[ref="indicator-${oldIndex}"]`);
    const newIndicator = document.querySelector(`[ref="indicator-${newIndex}"]`);

    if (oldIndicator && newIndicator) {
      // Animate the old indicator out
      tl.to(oldIndicator, {
        scaleX: 0,
        duration: 0.3,
        ease: 'power2.in'
      });

      // Animate the new indicator in
      tl.to(newIndicator, {
        scaleX: 1,
        duration: 0.3,
        ease: 'power2.out'
      }, "+=0.1");
    }
  }
});

// No magnetic effect or any other effects on buttons
const addMagneticEffect = (_element) => {
  // Return empty cleanup function
  return () => {};
};

// Animate logo highlight with GSAP
const animateLogoHighlight = (isHovering) => {
  if (!logoHighlight.value) {
    console.warn('Logo highlight element not found');
    return;
  }

  // Kill any existing animations on this element to prevent conflicts
  gsap.killTweensOf(logoHighlight.value);

  // Ensure the element has proper initial styling
  if (isHovering) {
    gsap.to(logoHighlight.value, {
      width: '100%',
      duration: 0.3,
      ease: 'power2.out'
    });
  } else {
    gsap.to(logoHighlight.value, {
      width: '0%',
      duration: 0.3,
      ease: 'power2.in'
    });
  }
};

// Create a variable to store cleanup functions
const cleanupFunctions = [];

// Handle cleanup when component is unmounted
onUnmounted(() => {
  // Clean up all event listeners
  window.removeEventListener('scroll', handleScroll);
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  cleanupFunctions.forEach(cleanup => cleanup && cleanup());
});

// Variables to track animation state
const animationTimeline = ref(null);
const animationState = ref({
  hasStarted: false,
  hasCompleted: false,
  isPaused: false
});

// Function to set all elements to their final state immediately
const setElementsToFinalState = () => {
  // Set navbar to final position
  gsap.set(navbar.value, {
    y: 0,
    opacity: 1
  });

  // Set nav links to final state
  gsap.set('.nav-link', {
    y: 0,
    opacity: 1,
    scale: 1
  });

  // Set logo to final state
  gsap.set('.text-xl.font-bold', {
    opacity: 1,
    scale: 1,
    rotation: 0
  });

  // Find and show the active indicator
  const activeIndex = navLinks.findIndex(link =>
    route.path === link.path || (link.path !== '/' && route.path.startsWith(link.path))
  );

  if (activeIndex !== -1) {
    const activeIndicator = document.querySelector(`[ref="indicator-${activeIndex}"]`);
    if (activeIndicator) {
      gsap.set(activeIndicator, {
        scaleX: 1,
        opacity: 1
      });
    }
  }

  // Mark animation as completed
  animationState.value.hasCompleted = true;
};

// Function to create the navbar animation timeline
const createNavbarAnimation = () => {
  // Hide all indicators initially with CSS but keep them in the flow
  document.querySelectorAll('.indicator-bar').forEach(indicator => {
    // Use opacity instead of visibility to allow animations
    gsap.set(indicator, {
      opacity: 0,
      transform: 'scaleX(0)'
    });
  });

  // Create a new timeline
  const tl = gsap.timeline({
    paused: true, // Start paused so we can control when it plays
    onComplete: () => {
      animationState.value.hasCompleted = true;
    }
  });

  // Animate navbar entrance
  tl.from(navbar.value, {
    y: -100,
    opacity: 0,
    duration: 1,
    ease: 'power3.out'
  });

  // Staggered entrance for nav links
  const linkElements = document.querySelectorAll('.nav-link');
  tl.from(linkElements, {
    transform: 'translateY(-20px) scale(0.8)',
    opacity: 0,
    stagger: 0.1,
    duration: 0.6,
    ease: 'back.out(1.7)'
  }, "-=0.5");

  // Animate the logo with a special effect
  tl.from('.text-xl.font-bold', {
    opacity: 0,
    transform: 'scale(0) rotate(-10deg)',
    duration: 0.8,
    ease: 'elastic.out(1, 0.5)'
  }, "-=1");

  // After the main animations, animate the active indicator
  tl.call(() => {
    // Find the active link
    const activeIndex = navLinks.findIndex(link =>
      route.path === link.path || (link.path !== '/' && route.path.startsWith(link.path))
    );

    if (activeIndex !== -1) {
      const activeIndicator = document.querySelector(`[ref="indicator-${activeIndex}"]`);
      if (activeIndicator) {
        // Create a fancy GSAP timeline for the indicator animation
        const indicatorTl = gsap.timeline();

        // First, animate a "drawing" effect from center
        indicatorTl.fromTo(activeIndicator,
          {
            scaleX: 0,
            opacity: 0,
            transformOrigin: 'center'
          },
          {
            scaleX: 1,
            opacity: 0.7,
            duration: 0.4,
            ease: 'power2.out'
          }
        )

        // Then add a slight bounce effect
        .to(activeIndicator, {
          scaleX: 0.9,
          duration: 0.1,
          ease: 'power1.in'
        })

        // Then expand to final size with glow
        .to(activeIndicator, {
          scaleX: 1,
          opacity: 1,
          duration: 0.3,
          ease: 'back.out(1.7)',
          onStart: () => {
            // Add a temporary glow effect
            gsap.to(activeIndicator, {
              boxShadow: '0 0 8px rgba(0, 255, 65, 0.6)',
              duration: 0.3
            });
          },
          onComplete: () => {
            // Remove the glow effect
            gsap.to(activeIndicator, {
              boxShadow: 'none',
              duration: 0.5
            });
          }
        });
      }
    }
  });

  return tl;
};

// Function to handle visibility change using the Page Visibility API
const handleVisibilityChange = () => {
  // Check if document is hidden (tab is not visible)
  const isHidden = document.hidden;

  if (isHidden) {
    // If animation is running but not completed, pause it
    if (animationTimeline.value && animationState.value.hasStarted && !animationState.value.hasCompleted) {
      animationTimeline.value.pause();
      animationState.value.isPaused = true;
    }
  } else {
    // If animation hasn't started yet, start it
    if (!animationState.value.hasStarted) {
      animationState.value.hasStarted = true;
      animationTimeline.value.play();
    }
    // If animation was paused, decide what to do
    else if (animationState.value.isPaused && !animationState.value.hasCompleted) {
      // Instead of resuming (which can look janky), just set to final state
      setElementsToFinalState();
    }
  }
};

// Animate the glass border
const animateGlassBorder = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) {
    return; // Exit if not in browser environment
  }

  // Need to use a different selector approach for pseudo-elements
  const navbarElement = document.querySelector('.navbar-glass');
  if (navbarElement) {
    // Create a style element for the animation
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes gradientMove {
        0% { background-position: 0% 0; }
        100% { background-position: 200% 0; }
      }
      .navbar-glass::after {
        animation: gradientMove 8s linear infinite alternate;
      }
    `;
    document.head.appendChild(styleElement);
  }
};

// All DOM operations should be inside onMounted
onMounted(() => {
  // Set up scroll event listener
  window.addEventListener('scroll', handleScroll);

  // Initial check for scroll position
  handleScroll();

  // Initialize logo highlight element
  if (logoHighlight.value) {
    gsap.set(logoHighlight.value, { width: '0%' });
  }

  // Initialize hamburger lines
  if (hamburgerLine1.value && hamburgerLine2.value && hamburgerLine3.value) {
    gsap.set([hamburgerLine1.value, hamburgerLine2.value, hamburgerLine3.value], {
      transformOrigin: 'center center'
    });

    // Position the lines
    gsap.set(hamburgerLine1.value, { y: -4 });
    gsap.set(hamburgerLine2.value, { y: 0 });
    gsap.set(hamburgerLine3.value, { y: 4 });
  }

  // Initialize mobile menu items with proper transforms
  // This ensures consistent behavior on first open
  nextTick(() => {
    const mobileLinks = document.querySelectorAll('.mobile-nav-link');
    const mobileArrows = document.querySelectorAll('.mobile-nav-arrow');

    if (mobileLinks.length > 0) {
      gsap.set(mobileLinks, {
        opacity: 0,
        transform: 'translateX(50px) rotateY(15deg)'
      });
    }

    if (mobileArrows.length > 0) {
      gsap.set(mobileArrows, {
        opacity: 0,
        transform: 'translateX(-10px)'
      });
    }
  });

  // Create the animation timeline
  animationTimeline.value = createNavbarAnimation();

  // Check if the page is visible initially
  if (typeof window !== 'undefined' && document) {
    const isInitiallyVisible = !document.hidden;

    if (isInitiallyVisible) {
      // If page is visible on load, start the animation immediately
      animationState.value.hasStarted = true;
      animationTimeline.value.play();
    } else {
      // If page is not visible on load, set elements to final state
      setElementsToFinalState();
    }
  }

  // Start the glass border animation
  animateGlassBorder();

  // Update active indicator color after a delay to ensure DOM is ready
  setTimeout(() => {
    updateActiveIndicatorColor();
  }, 500);
});

// Function to update the active indicator color
const updateActiveIndicatorColor = () => {
  try {
    // Get the primary color based on the current theme
    let primaryColor;

    if (themeStore.isProjectPage && themeStore.projectThemeClass && typeof themeStore.projectThemeClass === 'object') {
      primaryColor = themeStore.projectThemeClass.primary;
    } else {
      primaryColor = themeStore.isGameDev ? '#00FF41' : '#00A3FF';
    }

    // Find the active index
    const activeIndex = navLinks.findIndex(link =>
      route.path === link.path || (link.path !== '/' && route.path.startsWith(link.path))
    );

    if (activeIndex !== -1) {
      // Try to find the indicator using the class and index
      const indicators = document.querySelectorAll('.indicator-bar');

      if (indicators && indicators.length > 0 && activeIndex < indicators.length) {
        const activeIndicator = indicators[activeIndex];

        if (activeIndicator) {
          // Update the indicator glow
          gsap.to(activeIndicator, {
            boxShadow: `0 0 8px ${primaryColor}80`, // 50% opacity
            duration: 0.3
          });
        }
      }
    }
  } catch (error) {
    console.warn('Error in updateActiveIndicatorColor:', error);
  }
};

// Watch for theme changes
watch(
  [
    () => themeStore.isGameDev,
    () => themeStore.isProjectPage,
    () => themeStore.projectThemeClass,
    () => themeStore.themeColors
  ],
  () => {
    try {
      // Wait for DOM to be ready before updating colors
      setTimeout(() => {
        updateActiveIndicatorColor();
      }, 300);
    } catch (error) {
      // Silent fallback
    }
  },
  { immediate: false, deep: true }
);

// Set up visibility change listener in onMounted
onMounted(() => {
  // Only add event listener if we're in a browser environment
  if (typeof window !== 'undefined' && document) {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  // Set up hover animations for nav links
  // Only set up animations if we're in a browser environment
  if (typeof window !== 'undefined' && document) {
    const linkElements = document.querySelectorAll('.nav-link');
    linkElements.forEach((link, index) => {
      // Add magnetic effect
      const cleanup = addMagneticEffect(link);
      cleanupFunctions.push(cleanup);

      // Add fancy GSAP hover animations for the indicators
      link.addEventListener('mouseenter', () => {
        if (!isActiveRoute(navLinks[index].path)) {
          const indicator = document.querySelector(`[ref="indicator-${index}"]`);
          if (indicator) {
            // Create a hover-in timeline
            const hoverTl = gsap.timeline();

            // No need to change visibility - we'll use opacity

            // Animate from left to right with a slight bounce
            hoverTl.fromTo(indicator,
              {
                transform: 'scaleX(0)',
                opacity: 0,
                transformOrigin: 'left center'
              },
              {
                transform: 'scaleX(1)',
                opacity: 1,
                duration: 0.35,
                ease: 'power3.out'
              }
            )
            .to(indicator, {
              transform: 'scaleX(0.97)',
              duration: 0.1,
              ease: 'power1.in'
            })
            .to(indicator, {
              transform: 'scaleX(1)',
              duration: 0.15,
              ease: 'power1.out'
            });
          }
        }
      });

      link.addEventListener('mouseleave', () => {
        if (!isActiveRoute(navLinks[index].path)) {
          const indicator = document.querySelector(`[ref="indicator-${index}"]`);
          if (indicator) {
            // Animate out from right to left
            gsap.to(indicator, {
              transform: 'scaleX(0)',
              opacity: 0,
              transformOrigin: 'right center',
              duration: 0.25,
              ease: 'power2.in',
              // No need for onComplete callback - opacity 0 is enough
            });
          }
        }
      });
    });
  }
});
</script>

<style scoped>
/* Glass effect for navbar */
.navbar-glass {
  position: relative;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background-color: rgba(0, 0, 0, 0.7);
  /* Border and shadow set by CSS variables */
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.2);
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.1);
  transition: var(--theme-transition);
}

/* Inner glow effect */
.navbar-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(180deg,
    rgba(var(--primary-rgb), 0.03) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  pointer-events: none;
  transition: var(--theme-transition);
}

/* Glass-like gradient border at the bottom */
.navbar-glass::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(var(--primary-rgb), 0) 0%,
    rgba(var(--primary-rgb), 0.3) 20%,
    rgba(var(--primary-rgb), 0.5) 50%,
    rgba(var(--primary-rgb), 0.3) 80%,
    rgba(var(--primary-rgb), 0) 100%
  );
  background-size: 200% 100%;
  box-shadow: 0 0 8px rgba(var(--primary-rgb), 0.3);
  will-change: background-position;
  transition: var(--theme-transition);
}

/* Gradient text effect */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

/* Ensure proper positioning for particles */
.relative {
  position: relative;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure the navbar has proper z-index */
nav {
  z-index: 50;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

/* Mobile menu styles */
.mobile-nav-link {
  position: relative;
  overflow: hidden;
}

/* Simple nav link styling */
.nav-link {
  position: relative;
}

/* Make the active link more prominent - without text shadow */
.nav-link.text-white {
  font-weight: 600;
}

/* Subtle hover effect for nav links */
.nav-link:hover {
  color: white;
}

/* Style the indicator bar for proper animations */
.indicator-bar {
  position: relative;
  z-index: 1;
  will-change: transform, opacity;
  transform-origin: center;
  transition: background-color 0.3s ease, transform 0.3s ease, opacity 0.3s ease;
}

/* Hide the navigation particles containers completely */
nav [ref^="particles-"] {
  display: none;
  opacity: 0;
  visibility: hidden;
}
</style>