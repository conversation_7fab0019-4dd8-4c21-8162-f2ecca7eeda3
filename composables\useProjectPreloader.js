// Project preloading composable for performance optimization
import { ref, computed } from 'vue'

// Global state for preloaded data
const preloadedProjects = ref(null)
const preloadedProjectDetails = ref(new Map())
const preloadingState = ref({
  projects: false,
  projectDetails: new Set()
})

// Cache configuration
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours
const PRELOAD_DELAY = 2000 // Wait 2 seconds after page load before starting preload

export const useProjectPreloader = () => {
  const isPreloadingProjects = computed(() => preloadingState.value.projects)
  const isPreloadingProject = (slug) => preloadingState.value.projectDetails.has(slug)

  // Get cached project data from localStorage
  const getCachedData = (key) => {
    if (typeof window === 'undefined') return null

    try {
      const cached = localStorage.getItem(`preload_cache_${key}`)
      if (cached) {
        const { data, timestamp } = JSON.parse(cached)
        if (Date.now() - timestamp < CACHE_DURATION) {
          return data
        }
        // Remove expired cache
        localStorage.removeItem(`preload_cache_${key}`)
      }
    } catch (error) {
      console.warn('Cache read error:', error)
    }
    return null
  }

  // Store data in localStorage cache
  const setCachedData = (key, data) => {
    if (typeof window === 'undefined') return

    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(`preload_cache_${key}`, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('Cache write error:', error)
    }
  }

  // Preload all projects data in background
  const preloadProjects = async () => {
    if (preloadedProjects.value || preloadingState.value.projects) {
      return preloadedProjects.value
    }

    // Check cache first
    const cachedProjects = getCachedData('projects')
    if (cachedProjects) {
      preloadedProjects.value = cachedProjects
      return cachedProjects
    }

    preloadingState.value.projects = true

    try {
      const projects = await queryCollection("content")
        .select("title", "description", "meta", "path")
        .all()

      if (projects) {
        preloadedProjects.value = projects
        setCachedData('projects', projects)
      }

      return projects
    } catch (error) {
      console.warn('Failed to preload projects:', error)
      return null
    } finally {
      preloadingState.value.projects = false
    }
  }

  // Preload specific project details on hover
  const preloadProjectDetails = async (projectPath, options = {}) => {
    const { debounceMs = 300 } = options

    if (!projectPath) return null

    const slug = projectPath.replace('/projects/', '')

    // Return cached data if available
    if (preloadedProjectDetails.value.has(slug)) {
      return preloadedProjectDetails.value.get(slug)
    }

    // Check if already preloading
    if (preloadingState.value.projectDetails.has(slug)) {
      return null
    }

    // Check localStorage cache
    const cachedProject = getCachedData(`project_${slug}`)
    if (cachedProject) {
      preloadedProjectDetails.value.set(slug, cachedProject)
      return cachedProject
    }

    // Debounce the preloading to avoid excessive requests
    return new Promise((resolve) => {
      setTimeout(async () => {
        // Double-check if still needed after debounce
        if (preloadedProjectDetails.value.has(slug) ||
            preloadingState.value.projectDetails.has(slug)) {
          resolve(preloadedProjectDetails.value.get(slug) || null)
          return
        }

        preloadingState.value.projectDetails.add(slug)

        try {
          const projectData = await queryCollection("content")
            .path(projectPath)
            .first()

          if (projectData) {
            preloadedProjectDetails.value.set(slug, projectData)
            setCachedData(`project_${slug}`, projectData)
            resolve(projectData)
          } else {
            resolve(null)
          }
        } catch (error) {
          console.warn(`Failed to preload project ${slug}:`, error)
          resolve(null)
        } finally {
          preloadingState.value.projectDetails.delete(slug)
        }
      }, debounceMs)
    })
  }

  // Get preloaded projects (for instant access)
  const getPreloadedProjects = () => {
    return preloadedProjects.value || getCachedData('projects')
  }

  // Get preloaded project details (for instant access)
  const getPreloadedProjectDetails = (projectPath) => {
    if (!projectPath) return null

    const slug = projectPath.replace('/projects/', '')
    return preloadedProjectDetails.value.get(slug) || getCachedData(`project_${slug}`)
  }

  // Initialize background preloading after page load
  const initializeBackgroundPreloading = () => {
    if (typeof window === 'undefined') return

    console.log('🚀 Initializing background preloading...')

    // Wait for page to fully load before starting background preloading
    setTimeout(() => {
      // Only preload if user hasn't navigated away
      const currentPath = window.location.pathname
      console.log('📍 Current path:', currentPath)

      if (currentPath === '/' || currentPath === '/projects' || currentPath === '/projects/') {
        console.log('✅ Starting projects preload...')
        preloadProjects().then(() => {
          console.log('🎉 Projects preloaded successfully!')
        }).catch(error => {
          console.warn('❌ Projects preload failed:', error)
        })
      } else {
        console.log('⏭️ Skipping preload for path:', currentPath)
      }
    }, PRELOAD_DELAY)
  }

  // Clear all cached data (useful for development)
  const clearCache = () => {
    preloadedProjects.value = null
    preloadedProjectDetails.value.clear()
    preloadingState.value.projects = false
    preloadingState.value.projectDetails.clear()

    if (typeof window !== 'undefined') {
      // Clear localStorage cache
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('preload_cache_')) {
          localStorage.removeItem(key)
        }
      })
    }
  }

  // Get preloading statistics
  const getPreloadingStats = () => {
    return {
      projectsPreloaded: !!preloadedProjects.value,
      projectDetailsPreloaded: preloadedProjectDetails.value.size,
      isPreloadingProjects: preloadingState.value.projects,
      preloadingProjectsCount: preloadingState.value.projectDetails.size
    }
  }

  return {
    // Main preloading functions
    preloadProjects,
    preloadProjectDetails,
    initializeBackgroundPreloading,

    // Data access
    getPreloadedProjects,
    getPreloadedProjectDetails,

    // State
    isPreloadingProjects,
    isPreloadingProject,

    // Utilities
    clearCache,
    getPreloadingStats
  }
}
