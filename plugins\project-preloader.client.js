// Project preloader plugin for global initialization
import { useProjectPreloader } from '~/composables/useProjectPreloader'

export default defineNuxtPlugin((nuxtApp) => {
  // Initialize preloader on app mounted
  nuxtApp.hook('app:mounted', () => {
    const { initializeBackgroundPreloading } = useProjectPreloader()
    
    // Start background preloading if we're on the homepage or projects page
    const currentPath = window.location.pathname
    if (currentPath === '/' || currentPath === '/projects' || currentPath === '/projects/') {
      initializeBackgroundPreloading()
    }
  })
})
