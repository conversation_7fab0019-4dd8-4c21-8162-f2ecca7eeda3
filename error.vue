<template>
  <div class="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
    <!-- Particles background -->
    <ParticlesBackground />

    <!-- Floating geometric shapes -->
    <div class="absolute inset-0 pointer-events-none">
      <div ref="shape1" class="absolute w-20 h-20 border border-primary/20 rotate-45 opacity-30"></div>
      <div ref="shape2" class="absolute w-16 h-16 border border-secondary/20 rounded-full opacity-25"></div>
      <div ref="shape3" class="absolute w-12 h-12 border border-accent/20 opacity-20"></div>
      <div ref="shape4" class="absolute w-8 h-8 bg-primary/10 rounded-full opacity-40"></div>
      <div ref="shape5" class="absolute w-6 h-6 bg-secondary/10 rotate-45 opacity-35"></div>
    </div>

    <!-- Error content -->
    <div ref="errorContent" class="relative z-10 text-center px-4 max-w-4xl mx-auto">
      <!-- Error code with enhanced glow effect -->
      <div ref="errorCode" class="mb-12">
        <h1 class="text-8xl md:text-9xl lg:text-[12rem] font-bold text-primary text-glow mb-6 leading-none text-red-500">
          {{ error.statusCode }}
        </h1>
        <div ref="divider" class="h-1 w-48 bg-gradient-to-r from-transparent via-primary to-transparent mx-auto opacity-80 rounded-full"></div>
      </div>

      <!-- Error message with better typography -->
      <div ref="errorMessage" class="mb-16">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
          {{ getErrorTitle() }}
        </h2>
        <p class="text-gray-300 text-xl md:text-2xl leading-relaxed max-w-3xl mx-auto">
          {{ getErrorMessage() }}
        </p>
      </div>

      <!-- Enhanced action buttons -->
      <div ref="actionButtons" class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
        <!-- Go Home button with enhanced styling -->
        <NuxtLink
          to="/"
          class="group relative px-10 py-5 bg-gradient-to-r from-primary/20 to-primary/10 border border-primary/40 rounded-xl text-primary font-bold text-lg transition-all duration-500 hover:from-primary/30 hover:to-primary/20 hover:border-primary/60 hover:scale-110 hover:shadow-2xl hover:shadow-primary/25 focus:outline-none focus:ring-4 focus:ring-primary/30 glass-enhanced"
          @click="handleNavigation"
        >
          <span class="relative z-10 flex items-center gap-3">
            <Icon name="mdi:home" class="w-6 h-6" />
            Go Home
          </span>
          <!-- Enhanced hover effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/20 to-primary/0 opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-xl"></div>
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 rounded-xl"></div>
        </NuxtLink>

        <!-- Go Back button with enhanced styling -->
        <button
          @click="goBack"
          class="group relative px-10 py-5 bg-transparent border-2 border-gray-400/30 rounded-xl text-gray-300 font-bold text-lg transition-all duration-500 hover:border-gray-300/60 hover:text-white hover:scale-110 hover:shadow-2xl hover:shadow-gray-500/10 focus:outline-none focus:ring-4 focus:ring-gray-400/30 glass-enhanced"
        >
          <span class="relative z-10 flex items-center gap-3">
            <Icon name="mdi:arrow-left" class="w-6 h-6" />
            Go Back
          </span>
          <!-- Enhanced hover effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-gray-500/0 via-gray-500/10 to-gray-500/0 opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-xl"></div>
        </button>
      </div>

      <!-- Enhanced help section for 404 -->
      <div v-if="error.statusCode === 404" ref="helpSection" class="space-y-8">
        <!-- Quick navigation -->
        <div class="p-8 glass-enhanced rounded-2xl border border-primary/30 backdrop-blur-xl">
          <h3 class="text-2xl font-bold text-white mb-6 flex items-center gap-3">
            <Icon name="mdi:compass" class="w-7 h-7 text-primary" />
            Looking for something specific?
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <NuxtLink
              to="/projects"
              class="group flex items-center gap-4 p-4 rounded-xl bg-primary/5 border border-primary/20 text-gray-300 hover:text-primary hover:bg-primary/10 hover:border-primary/40 transition-all duration-300 hover:scale-105"
              @click="handleNavigation"
            >
              <Icon name="mdi:folder-multiple" class="w-6 h-6 text-primary group-hover:scale-110 transition-transform duration-300" />
              <span class="font-semibold">View Projects</span>
            </NuxtLink>
            <NuxtLink
              to="/about"
              class="group flex items-center gap-4 p-4 rounded-xl bg-secondary/5 border border-secondary/20 text-gray-300 hover:text-secondary hover:bg-secondary/10 hover:border-secondary/40 transition-all duration-300 hover:scale-105"
              @click="handleNavigation"
            >
              <Icon name="mdi:account" class="w-6 h-6 text-secondary group-hover:scale-110 transition-transform duration-300" />
              <span class="font-semibold">About Me</span>
            </NuxtLink>
            <NuxtLink
              to="/contact"
              class="group flex items-center gap-4 p-4 rounded-xl bg-accent/5 border border-accent/20 text-gray-300 hover:text-accent hover:bg-accent/10 hover:border-accent/40 transition-all duration-300 hover:scale-105"
              @click="handleNavigation"
            >
              <Icon name="mdi:email" class="w-6 h-6 text-accent group-hover:scale-110 transition-transform duration-300" />
              <span class="font-semibold">Contact</span>
            </NuxtLink>
          </div>
        </div>

        <!-- Project suggestions for project routes -->
        <div v-if="isProjectRoute" class="p-8 glass-enhanced rounded-2xl border border-secondary/30 backdrop-blur-xl">
          <h3 class="text-2xl font-bold text-white mb-6 flex items-center gap-3">
            <Icon name="mdi:lightbulb" class="w-7 h-7 text-secondary" />
            Maybe you were looking for one of these projects?
          </h3>
          <div v-if="availableProjects.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <NuxtLink
              v-for="project in availableProjects.slice(0, 6)"
              :key="project.path"
              :to="project.path"
              class="group flex items-center gap-3 p-4 rounded-xl bg-secondary/5 border border-secondary/20 text-gray-300 hover:text-secondary hover:bg-secondary/10 hover:border-secondary/40 transition-all duration-300 hover:scale-105"
              @click="handleNavigation"
            >
              <Icon name="mdi:folder" class="w-5 h-5 text-secondary group-hover:scale-110 transition-transform duration-300" />
              <span class="font-medium truncate">{{ project.title }}</span>
            </NuxtLink>
          </div>
          <div v-else class="text-center text-gray-400">
            <Icon name="mdi:loading" class="w-8 h-8 mx-auto mb-2 animate-spin" />
            <p>Loading available projects...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced floating decorative elements -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <div ref="floatingDot1" class="absolute w-3 h-3 bg-primary rounded-full opacity-60"></div>
      <div ref="floatingDot2" class="absolute w-2 h-2 bg-secondary rounded-full opacity-50"></div>
      <div ref="floatingDot3" class="absolute w-4 h-4 bg-accent rounded-full opacity-40"></div>
      <div ref="floatingDot4" class="absolute w-1.5 h-1.5 bg-primary rounded-full opacity-70"></div>
      <div ref="floatingDot5" class="absolute w-2.5 h-2.5 bg-secondary rounded-full opacity-45"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { gsap } from 'gsap'
import { useThemeStore } from '~/stores/theme-optimized'

// Get error from props
const props = defineProps({
  error: {
    type: Object,
    required: true
  }
})

// Theme store and route
const themeStore = useThemeStore()
const route = useRoute()

// Template refs for animations
const errorContent = ref(null)
const errorCode = ref(null)
const divider = ref(null)
const errorMessage = ref(null)
const actionButtons = ref(null)
const helpSection = ref(null)
const shape1 = ref(null)
const shape2 = ref(null)
const shape3 = ref(null)
const shape4 = ref(null)
const shape5 = ref(null)
const floatingDot1 = ref(null)
const floatingDot2 = ref(null)
const floatingDot3 = ref(null)
const floatingDot4 = ref(null)
const floatingDot5 = ref(null)

// State for project suggestions
const availableProjects = ref([])

// Check if this is a project route
const isProjectRoute = computed(() => {
  return route.path.startsWith('/projects/') && route.path !== '/projects/'
})

// Enhanced error messages for project routes
const getErrorTitle = () => {
  if (props.error.statusCode === 404 && isProjectRoute.value) {
    return 'Project Not Found'
  }

  switch (props.error.statusCode) {
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Internal Server Error'
    case 403:
      return 'Access Forbidden'
    case 401:
      return 'Unauthorized'
    default:
      return 'Something Went Wrong'
  }
}

const getErrorMessage = () => {
  if (props.error.statusCode === 404 && isProjectRoute.value) {
    return "The project you're looking for doesn't exist or may have been moved. Check out some of my other projects below!"
  }

  switch (props.error.statusCode) {
    case 404:
      return "The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."
    case 500:
      return "We're experiencing some technical difficulties. Please try again later or contact support if the problem persists."
    case 403:
      return "You don't have permission to access this resource. Please check your credentials or contact an administrator."
    case 401:
      return "You need to be authenticated to access this page. Please log in and try again."
    default:
      return "An unexpected error occurred. Please try refreshing the page or contact support if the issue continues."
  }
}

// SEO meta
useHead({
  title: computed(() => `${props.error.statusCode} - ${getErrorTitle()}`),
  meta: [
    { name: 'description', content: computed(() => getErrorMessage()) },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})

// Load available projects for suggestions
const loadAvailableProjects = async () => {
  if (!isProjectRoute.value) return

  try {
    const projects = await queryCollection("content")
      .select("title", "path")
      .all()

    // Filter out the current path and limit to actual projects
    availableProjects.value = projects
      .filter(project =>
        project.path.startsWith('/projects/') &&
        project.path !== route.path &&
        project.title
      )
      .sort(() => Math.random() - 0.5) // Randomize order
  } catch (error) {
    console.warn('Failed to load available projects:', error)
    availableProjects.value = []
  }
}

// Navigation handlers
const goBack = () => {
  if (typeof window !== 'undefined' && window.history.length > 1) {
    window.history.back()
  } else {
    navigateTo('/')
  }
}

const handleNavigation = () => {
  // Clear any error state
  clearError({ redirect: '/' })
}

// GSAP animations
const initAnimations = async () => {
  await nextTick()

  if (!errorContent.value) return

  // Set initial states
  gsap.set([errorCode.value, errorMessage.value, actionButtons.value, helpSection.value], {
    opacity: 0,
    y: 50
  })

  // Animate floating shapes
  if (shape1.value) {
    gsap.set(shape1.value, { top: '20%', left: '15%' })
    gsap.to(shape1.value, {
      rotation: 360,
      duration: 20,
      repeat: -1,
      ease: 'none'
    })
    gsap.to(shape1.value, {
      y: '+=30',
      duration: 4,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    })
  }

  if (shape2.value) {
    gsap.set(shape2.value, { top: '60%', right: '20%' })
    gsap.to(shape2.value, {
      scale: 1.2,
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    })
  }

  if (shape3.value) {
    gsap.set(shape3.value, { bottom: '30%', left: '10%' })
    gsap.to(shape3.value, {
      rotation: -180,
      duration: 15,
      repeat: -1,
      ease: 'none'
    })
  }

  if (shape4.value) {
    gsap.set(shape4.value, { top: '40%', right: '15%' })
    gsap.to(shape4.value, {
      x: '+=20',
      duration: 5,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    })
  }

  if (shape5.value) {
    gsap.set(shape5.value, { bottom: '20%', right: '30%' })
    gsap.to(shape5.value, {
      rotation: 45,
      scale: 0.8,
      duration: 6,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    })
  }

  // Animate floating dots
  const dots = [floatingDot1.value, floatingDot2.value, floatingDot3.value, floatingDot4.value, floatingDot5.value]
  dots.forEach((dot, index) => {
    if (dot) {
      // Random positions
      gsap.set(dot, {
        top: `${Math.random() * 80 + 10}%`,
        left: `${Math.random() * 80 + 10}%`
      })

      // Floating animation
      gsap.to(dot, {
        y: `+=${Math.random() * 40 + 20}`,
        x: `+=${Math.random() * 30 + 15}`,
        duration: Math.random() * 3 + 4,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
        delay: index * 0.5
      })
    }
  })

  // Main content animation timeline
  const tl = gsap.timeline()

  tl.to(errorCode.value, {
    opacity: 1,
    y: 0,
    duration: 1,
    ease: 'power3.out'
  })
  .to(divider.value, {
    scaleX: 1,
    duration: 0.8,
    ease: 'power2.out'
  }, '-=0.5')
  .to(errorMessage.value, {
    opacity: 1,
    y: 0,
    duration: 0.8,
    ease: 'power3.out'
  }, '-=0.3')
  .to(actionButtons.value, {
    opacity: 1,
    y: 0,
    duration: 0.8,
    ease: 'power3.out'
  }, '-=0.3')

  if (helpSection.value) {
    tl.to(helpSection.value, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power3.out'
    }, '-=0.3')
  }

  // Set initial scale for divider
  gsap.set(divider.value, { scaleX: 0 })
}

// Initialize theme and animations on mount
onMounted(async () => {
  try {
    await themeStore.init()
    await loadAvailableProjects()
    await initAnimations()
  } catch (error) {
    console.warn('Error initializing error page:', error)
  }
})

// Log error for debugging (only in development)
if (process.dev) {
  console.error('Error page rendered:', props.error)
  console.log('Is project route:', isProjectRoute.value)
  console.log('Current path:', route.path)
}
</script>

<style scoped>
/* Enhanced styles for the error page */
.text-glow {
  text-shadow:
    0 0 20px rgb(var(--primary-rgb) / 0.8),
    0 0 40px rgb(var(--primary-rgb) / 0.6),
    0 0 60px rgb(var(--primary-rgb) / 0.4),
    0 0 80px rgb(var(--primary-rgb) / 0.2);
}

.glass-enhanced {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced responsive typography */
@media (max-width: 640px) {
  .text-8xl {
    font-size: 4rem;
  }

  .text-9xl {
    font-size: 5rem;
  }

  .lg\:text-\[12rem\] {
    font-size: 6rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .lg\:text-\[12rem\] {
    font-size: 8rem;
  }
}

/* Enhanced button hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Floating animations for decorative elements */
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(-5px) translateX(-5px);
  }
  75% {
    transform: translateY(-15px) translateX(3px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgb(var(--primary-rgb) / 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgb(var(--primary-rgb) / 0.8);
  }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Custom animations for shapes */
.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

/* Enhanced glass effect for containers */
.glass-container {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states for accessibility */
.focus\:ring-4:focus {
  box-shadow: 0 0 0 4px var(--ring-color, rgba(59, 130, 246, 0.3));
}

/* Improved text readability */
.text-shadow-soft {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Custom scrollbar for project suggestions */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Enhanced mobile responsiveness */
@media (max-width: 480px) {
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .gap-6 {
    gap: 1rem;
  }

  .p-8 {
    padding: 1.5rem;
  }

  .px-10 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .py-5 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
